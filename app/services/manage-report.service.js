/* models */
const Report = require('../models/report.model');
const Scope = require('../models/scope.model');
const UserReport = require('../models/user-report.model');
const UserReportAnswer = require('../models/user-report-answer.model');

// utils
const { toObjectId } = require('../utils/common.utils');
const commonfunctionsUtils = require('../utils/common-function.utils');

/**
 * Create Report
 *
 * @param {*} requestData
 * @returns
 */
exports.createReport = async (requestData, session) => {
  const report = new Report(requestData);
  return report.save({ session });
};

exports.getReports = async (filter, page, perPage, sort) => {
  return Report.find(filter, this.removeSelectFields())
    .sort({ sortOrder: sort ?? 1 })
    .limit(perPage)
    .skip(page * perPage)
    .populate(this.populateFields());
};

exports.updateReportById = async (id, requestData) =>
  Report.findByIdAndUpdate(id, { $set: requestData }, { new: true });

exports.getSingleReportByFilter = async filter => {
  return Report.findOne(filter, this.removeSelectFields()).populate(this.populateFields());
};

exports.deleteReport = async (id, requestData) => {
  return Report.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

exports.removeSelectFields = () => {
  return {
    updatedBy: 0,
    updatedAt: 0,
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  };
};

exports.populateFields = () => {
  return [
    {
      path: 'project',
      select: { _id: 1, title: 1 },
      strictPopulate: false,
    },
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ];
};

/**
 * Get One Report Id
 *
 * @param {*} filter
 * @returns
 */
exports.getOneReportId = async filter => {
  let agregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: {
        path: '$project',
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: {
        path: '$account',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: {
        path: '$createdBy',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
        as: 'locations',
      },
    },
    {
      $lookup: {
        from: 'assets',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, cableName: 1, manufacturer: 1, typeMm2: 1 } }],
        as: 'assets',
      },
    },
    {
      $lookup: {
        from: 'scopes',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'scope',
      },
    },
    {
      $unwind: {
        path: '$scopes',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        title: 1,
        project: 1,
        type: 1,
        status: 1,
        isProgressable: 1,
        isPublish: 1,
        sortOrder: 1,
        locations: 1,
        assets: 1,
        scope: 1,
        account: 1,
        createdBy: 1,
        createdAt: 1,
      },
    },
  ];

  return await Report.aggregate(agregateFunction);
};

/**
 * Get Report IDs
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 */
exports.getReportsIDs = async (filter, page = null, perPage = null, sort = null) => {
  let agregateFunction = [
    {
      $match: filter,
    },
    {
      $sort: { createdAt: sort ?? -1 },
    },
  ];

  if (page && perPage) {
    agregateFunction.push({
      $skip: Number.parseInt(page) * Number.parseInt(perPage),
    });
    agregateFunction.push({
      $limit: Number.parseInt(perPage),
    });
  }
  agregateFunction.push({
    $project: {
      _id: 1,
      title: 1,
      project: 1,
      type: 1,
      status: 1,
      isProgressable: 1,
      isPublish: 1,
      sortOrder: 1,
      locations: 1,
      assets: 1,
      scope: 1,
      account: 1,
      createdBy: 1,
      createdAt: 1,
    },
  });
  return await Report.aggregate(agregateFunction);
};

/**
 * Get Report Details
 *
 * @param {*} filter
 */
exports.getReportDetails = async filter => {
  let agreegateFunction = [
    {
      $match: { _id: filter },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: {
        path: '$project',
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: {
        path: '$account',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: {
        path: '$createdBy',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
        as: 'locations',
      },
    },
    {
      $lookup: {
        from: 'assets',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, cableName: 1 } }],
        as: 'assets',
      },
    },
    {
      $lookup: {
        from: 'scopes',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'scope',
      },
    },
    {
      $unwind: {
        path: '$scopes',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        title: 1,
        project: 1,
        type: 1,
        status: 1,
        isProgressable: 1,
        isPublish: 1,
        sortOrder: 1,
        locations: 1,
        assets: 1,
        scope: 1,
        account: 1,
        createdBy: 1,
        createdAt: 1,
      },
    },
  ];
  return await Report.aggregate(agreegateFunction);
};

/**
 * Get Report Details with Questions
 *
 * @param {*} filter
 */
exports.getReportDetailsWithQuestions = async filter => {
  let agreegateFunction = [
    {
      $match: { _id: filter },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: '_id',
        foreignField: 'report',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          { $sort: { sortOrder: 1 } },
          {
            $project: {
              title: 1,
              sortOrder: 1,
              duration: 1,
              isRequired: 1,
              weight: 1,
              supportedContent: 1,
              answerTypes: 1,
              createdBy: 1,
              createdAt: 1,
            },
          },
          {
            $lookup: {
              from: 'report-question-answers',
              localField: '_id',
              foreignField: 'reportQuestion',
              pipeline: [
                {
                  $match: {
                    deletedAt: null,
                  },
                },
                { $sort: { sortOrder: 1 } },
                {
                  $project: {
                    _id: 1,
                    title: {
                      $filter: {
                        input: '$title',
                        as: 'titleObj',
                        cond: { $eq: ['$$titleObj.isActive', true] },
                      },
                    },
                    parameterType: 1,
                    option: 1,
                    range: 1,
                    sortOrder: 1,
                    numberOfAnswers: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'parameter-types',
                    localField: 'parameterType',
                    foreignField: '_id',
                    pipeline: [{ $project: { name: 1, uniqueKey: 1, isActive: 1 } }],
                    as: 'parameterType',
                  },
                },
                {
                  $unwind: {
                    path: '$parameterType',
                  },
                },
              ],
              as: 'answerTypes',
            },
          },
        ],
        as: 'reportQuestions',
      },
    },
    {
      $project: {
        reportQuestions: 1,
      },
    },
  ];
  return await Report.aggregate(agreegateFunction);
};

exports.getReportWithQuestion = async (
  filter,
  page = null,
  perPage = null,
  sort = null,
  questions = true
) => {
  let agreegateFunction = [
    {
      $match: filter,
    },
    {
      $sort: { createdAt: sort ?? -1 },
    },
  ];

  if (page && perPage) {
    agreegateFunction.push({
      $skip: Number.parseInt(page) * Number.parseInt(perPage),
    });
    agreegateFunction.push({
      $limit: Number.parseInt(perPage),
    });
  }

  agreegateFunction.push(
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: {
        path: '$project',
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: {
        path: '$account',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: {
        path: '$createdBy',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
        as: 'locations',
      },
    },
    {
      $lookup: {
        from: 'assets',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, cableName: 1 } }],
        as: 'assets',
      },
    },
    {
      $lookup: {
        from: 'scopes',
        localField: '_id',
        foreignField: 'reports',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'scope',
      },
    },
    {
      $unwind: {
        path: '$scopes',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: '_id',
        foreignField: 'report',
        pipeline: [{ $match: { deletedAt: null, duration: { $gt: 0 } } }],
        as: 'reportQue',
      },
    },
    {
      $addFields: {
        durationQuestions: { $sum: '$reportQue.duration' },
      },
    }
  );

  if (questions) {
    agreegateFunction.push({
      $lookup: {
        from: 'report-questions',
        localField: '_id',
        foreignField: 'report',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          { $sort: { sortOrder: 1 } },
          {
            $project: {
              title: 1,
              sortOrder: 1,
              duration: 1,
              isRequired: 1,
              weight: 1,
              supportedContent: 1,
              answerTypes: 1,
              createdBy: 1,
              createdAt: 1,
            },
          },
          {
            $lookup: {
              from: 'report-question-answers',
              localField: '_id',
              foreignField: 'reportQuestion',
              pipeline: [
                {
                  $match: {
                    deletedAt: null,
                  },
                },
                { $sort: { createdAt: 1 } },
                {
                  $project: {
                    _id: 1,
                    title: {
                      $filter: {
                        input: '$title',
                        as: 'titleObj',
                        cond: { $eq: ['$$titleObj.isActive', true] },
                      },
                    },
                    parameterType: 1,
                    option: 1,
                    range: 1,
                    numberOfAnswers: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'parameter-types',
                    localField: 'parameterType',
                    foreignField: '_id',
                    pipeline: [{ $project: { name: 1, uniqueKey: 1, isActive: 1 } }],
                    as: 'parameterType',
                  },
                },
                {
                  $unwind: {
                    path: '$parameterType',
                  },
                },
              ],
              as: 'answerTypes',
            },
          },
        ],
        as: 'reportQuestions',
      },
    });
  }

  agreegateFunction.push({
    $project: {
      _id: 1,
      title: 1,
      project: 1,
      type: 1,
      status: 1,
      isProgressable: 1,
      isPublish: 1,
      sortOrder: 1,
      locations: 1,
      assets: 1,
      scope: 1,
      questionDuration: '$durationQuestions',
      reportQuestions: 1,
      account: 1,
      createdBy: 1,
      createdAt: 1,
    },
  });
  return await Report.aggregate(agreegateFunction);
};

exports.reportCalculation = async filter => {
  const scopeCal = await Scope.aggregate([
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'reports',
        foreignField: '_id',
        pipeline: [
          { $match: { deletedAt: null } },
          { $project: { _id: 1, title: 1, type: 1 } },
          {
            $lookup: {
              from: 'locations',
              localField: '_id',
              foreignField: 'reports',
              pipeline: [
                { $match: { deletedAt: null } },
                {
                  $group: { _id: null, total: { $sum: 1 } },
                },
              ],
              as: 'totalLocation',
            },
          },
          {
            $unwind: { path: '$totalLocation', preserveNullAndEmptyArrays: true },
          },
          {
            $lookup: {
              from: 'assets',
              localField: '_id',
              foreignField: 'reports',
              pipeline: [
                { $match: { deletedAt: null } },
                {
                  $addFields: {
                    fromLocationArray: {
                      $cond: {
                        if: { $isArray: '$fromLocation' },
                        then: '$fromLocation',
                        else: ['$fromLocation'],
                      },
                    },
                    toLocationArray: {
                      $cond: {
                        if: { $isArray: '$toLocation' },
                        then: '$toLocation',
                        else: ['$toLocation'],
                      },
                    },
                  },
                },
                {
                  $addFields: {
                    uniqueLocations: {
                      $setUnion: ['$fromLocationArray', '$toLocationArray'],
                    },
                  },
                },
                {
                  $unwind: '$uniqueLocations',
                },
                {
                  $group: {
                    _id: '$uniqueLocations',
                    total: { $sum: 1 },
                  },
                },
                {
                  $group: {
                    _id: null,
                    totalUniqueLocations: { $sum: 1 },
                  },
                },
              ],
              as: 'totalAsset',
            },
          },
          {
            $unwind: { path: '$totalAsset', preserveNullAndEmptyArrays: true },
          },
          {
            $lookup: {
              from: 'locations',
              localField: '_id',
              foreignField: 'reports',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                { $project: { _id: 1, title: 1 } },
              ],
              as: 'locations',
            },
          },
          {
            $lookup: {
              from: 'assets',
              localField: '_id',
              foreignField: 'reports',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                { $project: { _id: 1, cableName: 1, fromLocation: 1, toLocation: 1 } },
              ],
              as: 'assets',
            },
          },
          {
            $lookup: {
              from: 'report-questions',
              localField: '_id',
              foreignField: 'report',
              pipeline: [
                { $match: { deletedAt: null } },
                {
                  $group: { _id: null, total: { $sum: '$duration' } },
                },
              ],
              as: 'durationReport',
            },
          },
          {
            $unwind: '$durationReport',
          },
          {
            $addFields: {
              totalReportRequired: {
                $cond: {
                  if: { $eq: ['$type', 'location'] },
                  then: '$totalLocation.total',
                  else: {
                    $cond: {
                      if: {
                        $or: [
                          { $eq: ['$type', 'asset_per_location'] },
                          { $eq: ['$type', 'multiple_assets'] },
                        ],
                      },
                      then: '$totalAsset.totalUniqueLocations',
                      else: 0,
                    },
                  },
                },
              },
              durationReport: '$durationReport.total',
            },
          },
          {
            $addFields: {
              totalDurationReport: {
                $multiply: ['$totalReportRequired', '$durationReport'],
              },
            },
          },
        ],
        as: 'reports',
      },
    },
    {
      $unwind: {
        path: '$reports',
      },
    },
    {
      //group data for scope
      $group: {
        _id: { _id: '$_id', name: '$name' },
        sortOrder: { $first: '$sortOrder' },
        reports: { $push: '$reports' },
        totalDurationOfAllScopes: { $sum: '$reports.totalDurationReport' },
      },
    },
    {
      //group data for all report
      $group: {
        _id: null,
        scopeData: {
          $addToSet: {
            _id: '$_id._id',
            name: '$_id.name',
            sortOrder: '$sortOrder',
            reports: '$reports',
            totalDurationOfAllScopes: '$totalDurationOfAllScopes',
          },
        },
        totalDurationOfAllReports: { $sum: '$totalDurationOfAllScopes' },
      },
    },
    {
      $addFields: {
        scopeData: {
          $map: {
            input: '$scopeData',
            as: 'scope',
            in: {
              $mergeObjects: [
                '$$scope',
                {
                  reports: {
                    $map: {
                      input: '$$scope.reports',
                      as: 'report',
                      in: {
                        $mergeObjects: [
                          '$$report',
                          {
                            // report progress on each location or asset
                            reportProgress: {
                              $cond: {
                                if: {
                                  $and: [
                                    { $gt: ['$$report.totalDurationReport', 0] },
                                    { $gt: ['$totalDurationOfAllReports', 0] },
                                  ],
                                },
                                then: {
                                  $round: [
                                    {
                                      $multiply: [
                                        {
                                          $divide: [
                                            '$$report.totalDurationReport',
                                            '$totalDurationOfAllReports',
                                          ],
                                        },
                                        100,
                                      ],
                                    },
                                    2,
                                  ],
                                },
                                else: 0,
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                },
              ],
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: 'user-reports',
        localField: 'scopeData.reports._id',
        foreignField: 'report',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          { $sort: { createdAt: -1 } },
          {
            $project: {
              _id: 1,
              report: 1,
              location: 1,
              asset: 1,
              status: 1,
              createdAt: 1,
              updatedAt: 1,
            },
          },
          {
            $lookup: {
              from: 'locations',
              localField: 'location',
              foreignField: '_id',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $project: { _id: 1, title: 1 },
                },
              ],
              as: 'location',
            },
          },
          {
            $unwind: '$location',
          },
          {
            $lookup: {
              from: 'assets',
              localField: 'asset',
              foreignField: '_id',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $project: { _id: 1, cableName: 1, fromLocation: 1, toLocation: 1 },
                },
                {
                  $lookup: {
                    from: 'locations',
                    localField: 'fromLocation',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $match: { deletedAt: null },
                      },
                      {
                        $project: { _id: 1, title: 1 },
                      },
                    ],
                    as: 'fromLocation',
                  },
                },
                {
                  $unwind: '$fromLocation',
                },
                {
                  $lookup: {
                    from: 'locations',
                    localField: 'toLocation',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $match: { deletedAt: null },
                      },
                      {
                        $project: { _id: 1, title: 1 },
                      },
                    ],
                    as: 'toLocation',
                  },
                },
                {
                  $unwind: '$toLocation',
                },
              ],
              as: 'asset',
            },
          },
          {
            $unwind: {
              path: '$asset',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: 'user-report-answers',
              localField: '_id',
              foreignField: 'userReport',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $group: {
                    _id: '$reportQuestion',
                    total: {
                      $sum: 1,
                    },
                  },
                },
              ],
              as: 'totalAnswers',
            },
          },
          {
            $lookup: {
              from: 'report-questions',
              localField: 'totalAnswers._id',
              foreignField: '_id',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $group: { _id: null, total: { $sum: '$duration' } },
                },
              ],
              as: 'userQuestionAnswerDuration',
            },
          },
          {
            $unwind: {
              path: '$userQuestionAnswerDuration',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: 'reports',
              localField: 'report',
              foreignField: '_id',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                {
                  $project: { _id: 1, title: 1, type: 1 },
                },
                {
                  $lookup: {
                    from: 'report-questions',
                    localField: '_id',
                    foreignField: 'report',
                    pipeline: [
                      { $match: { deletedAt: null } },
                      {
                        $group: { _id: null, total: { $sum: '$duration' } },
                      },
                    ],
                    as: 'durationReport',
                  },
                },
                {
                  $unwind: '$durationReport',
                },
              ],
              as: 'report',
            },
          },
          {
            $unwind: '$report',
          },
          {
            $addFields: {
              reportProgress: {
                $cond: {
                  if: {
                    $and: [
                      { $gt: ['$userQuestionAnswerDuration.total', 0] },
                      { $gt: ['$report.durationReport.total', 0] },
                    ],
                  },
                  then: {
                    $round: [
                      {
                        $multiply: [
                          {
                            $divide: [
                              '$userQuestionAnswerDuration.total',
                              '$report.durationReport.total',
                            ],
                          },
                          100,
                        ],
                      },
                      2,
                    ],
                  },
                  else: 0,
                },
              },
            },
          },
          {
            $group: {
              _id: {
                locationId: '$location._id',
                location: '$location.title',
                reportId: '$report._id',
                report: '$report.title',
              },
              assets: { $push: '$asset' },
              reportProgress: { $push: '$reportProgress' },
              totalReportProgress: { $sum: '$reportProgress' },
              durationReport: { $first: '$report.durationReport.total' },
              createdAt: { $last: '$createdAt' },
              updatedAt: { $last: '$updatedAt' },
            },
          },
          {
            $addFields: {
              reportComplitionInPrecentage: {
                $cond: {
                  if: { $in: [100, '$reportProgress'] },
                  then: 100,
                  else: '$totalReportProgress',
                },
              },
              reportComplition: {
                $cond: {
                  if: { $in: [100, '$reportProgress'] },
                  then: 1,
                  else: { $round: [{ $divide: ['$totalReportProgress', 100] }, 2] },
                },
              },
            },
          },
        ],
        as: 'userReports',
      },
    },
    {
      $addFields: {
        //scope report calculation
        scopes: {
          $mergeObjects: {
            scopeData: '$scopeData',
          },
        },
        //users report calculation
        rows: {
          $mergeObjects: {
            userReports: '$userReports',
          },
        },
      },
    },
    {
      $addFields: {
        scopes: {
          $mergeObjects: [
            '$scopes',
            {
              scopeData: {
                $map: {
                  input: '$scopes.scopeData',
                  as: 'scopeData',
                  in: {
                    $mergeObjects: [
                      '$$scopeData',
                      {
                        reports: {
                          $map: {
                            input: '$$scopeData.reports',
                            as: 'report',
                            in: {
                              $let: {
                                vars: {
                                  matchedUserReports: {
                                    $filter: {
                                      input: '$rows.userReports',
                                      as: 'userReport',
                                      cond: { $eq: ['$$userReport._id.reportId', '$$report._id'] },
                                    },
                                  },
                                },
                                in: {
                                  $let: {
                                    vars: {
                                      reportComplitionProgress: {
                                        $reduce: {
                                          input: '$$matchedUserReports',
                                          initialValue: 0,
                                          in: { $add: ['$$value', '$$this.reportComplition'] },
                                        },
                                      },
                                    },
                                    in: {
                                      $mergeObjects: [
                                        '$$report',
                                        {
                                          reportComplitionProgress: '$$reportComplitionProgress',
                                          totalReportComplition: {
                                            $cond: {
                                              if: { $gt: ['$$report.totalDurationReport', 0] },
                                              then: {
                                                $round: [
                                                  {
                                                    $multiply: [
                                                      {
                                                        $divide: [
                                                          {
                                                            $multiply: [
                                                              '$$reportComplitionProgress',
                                                              '$$report.durationReport',
                                                            ],
                                                          },
                                                          '$$report.totalDurationReport',
                                                        ],
                                                      },
                                                      100,
                                                    ],
                                                  },
                                                  2,
                                                ],
                                              },
                                              else: 0,
                                            },
                                          },
                                        },
                                      ],
                                    },
                                  },
                                },
                              },
                            },
                          },
                        },
                      },
                    ],
                  },
                },
              },
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalCompletionPercentage: {
          $arrayElemAt: [
            {
              $map: {
                input: '$scopes.scopeData',
                as: 'scopeData',
                in: {
                  $round: [
                    {
                      $divide: [
                        {
                          $sum: {
                            $map: {
                              input: '$$scopeData.reports',
                              as: 'report',
                              in: {
                                $multiply: [
                                  '$$report.reportProgress',
                                  '$$report.totalReportComplition',
                                ],
                              },
                            },
                          },
                        },
                        100,
                      ],
                    },
                    2,
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $project: {
        _id: 0,
        scopes: 1,
        rows: 1,
        totalDurationOfAllReports: '$totalDurationOfAllReports',
        totalCompletionPercentage: 1,
      },
    },
  ]);

  return scopeCal;
};

exports.getUserReportDataForReportCalculation = async requestData => {
  let userReportRows = [];
  for (let scope of requestData.scopeData) {
    for (let report of scope.reports) {
      let commonAggregateData = [
        {
          $lookup: {
            from: 'locations',
            localField: 'location',
            foreignField: '_id',
            pipeline: [
              {
                $match: { deletedAt: null },
              },
              {
                $project: { _id: 1, title: 1 },
              },
            ],
            as: 'location',
          },
        },
        {
          $unwind: '$location',
        },
        {
          $lookup: {
            from: 'user-report-answers',
            localField: '_id',
            foreignField: 'userReport',
            pipeline: [
              {
                $match: { deletedAt: null },
              },
              {
                $group: {
                  _id: '$reportQuestion',
                  total: {
                    $sum: 1,
                  },
                },
              },
            ],
            as: 'totalAnswers',
          },
        },
        {
          $lookup: {
            from: 'report-questions',
            localField: 'totalAnswers._id',
            foreignField: '_id',
            pipeline: [
              {
                $match: { deletedAt: null },
              },
              {
                $group: { _id: null, total: { $sum: '$duration' } },
              },
            ],
            as: 'userQuestionAnswerDuration',
          },
        },
        {
          $unwind: {
            path: '$userQuestionAnswerDuration',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'reports',
            localField: 'report',
            foreignField: '_id',
            pipeline: [
              {
                $match: { deletedAt: null },
              },
              {
                $project: { _id: 1, title: 1, type: 1 },
              },
              {
                $lookup: {
                  from: 'report-questions',
                  localField: '_id',
                  foreignField: 'report',
                  pipeline: [
                    { $match: { deletedAt: null } },
                    {
                      $group: { _id: null, total: { $sum: '$duration' } },
                    },
                  ],
                  as: 'durationReport',
                },
              },
              {
                $unwind: '$durationReport',
              },
            ],
            as: 'report',
          },
        },
        {
          $unwind: '$report',
        },
        {
          $addFields: {
            reportProgress: {
              $cond: {
                if: {
                  $and: [
                    { $gt: ['$userQuestionAnswerDuration.total', 0] },
                    { $gt: ['$report.durationReport.total', 0] },
                  ],
                },
                then: {
                  $round: [
                    {
                      $multiply: [
                        {
                          $divide: [
                            '$userQuestionAnswerDuration.total',
                            '$report.durationReport.total',
                          ],
                        },
                        100,
                      ],
                    },
                    2,
                  ],
                },
                else: 0,
              },
            },
          },
        },
        {
          $group: {
            _id: {
              locationId: '$location._id',
              location: '$location.title',
              reportId: '$report._id',
              report: '$report.title',
              reportType: '$report.type',
            },
            reportProgress: { $push: '$reportProgress' },
            totalReportProgress: { $sum: '$reportProgress' },
            durationReport: { $first: '$report.durationReport.total' },
            createdAt: { $last: '$createdAt' },
            updatedAt: { $last: '$updatedAt' },
          },
        },
        {
          $addFields: {
            reportComplitionInPrecentage: {
              $cond: {
                if: { $in: [100, '$reportProgress'] },
                then: 100,
                else: '$totalReportProgress',
              },
            },
            reportComplition: {
              $cond: {
                if: { $in: [100, '$reportProgress'] },
                then: 1,
                else: { $round: [{ $divide: ['$totalReportProgress', 100] }, 2] },
              },
            },
          },
        },
        {
          $project: {
            _id: 1,
            reportProgress: 1,
            totalReportProgress: 1,
            durationReport: 1,
            createdAt: 1,
            updatedAt: 1,
            reportComplitionInPrecentage: 1,
            reportComplition: 1,
          },
        },
      ];

      let commonFileterUserReport = {
        reportProgress: [],
        totalReportProgress: 0,
        durationReport: 0,
        createdAt: null,
        updatedAt: null,
        reportComplitionInPrecentage: 0,
        reportComplition: 0,
      };

      let commonFilterIds = {
        reportId: report._id,
        report: report.title,
        reportType: report.type,
      };
      if (report.type === 'location') {
        for (let location of report.locations) {
          let userReportAggregate = [
            {
              $match: {
                report: toObjectId(report._id),
                location: toObjectId(location._id),
                deletedAt: null,
              },
            },
            ...commonAggregateData,
          ];

          let userReport = await UserReport.aggregate(userReportAggregate);

          let filterUserReport = {
            _id: {
              locationId: location._id,
              location: location.title,
              ...commonFilterIds,
            },
            ...commonFileterUserReport,
          };

          if (userReport.length > 0) {
            filterUserReport = userReport[0];
          }
          userReportRows.push(filterUserReport);
        }
      } else {
        for (let asset of report.assets) {
          let userReportAggregate = [
            {
              $match: {
                report: toObjectId(report._id),
                location: toObjectId(asset._id.locationId),
                deletedAt: null,
              },
            },
            ...commonAggregateData,
          ];

          let userReport = await UserReport.aggregate(userReportAggregate);

          let filterUserReport = {
            _id: {
              locationId: asset._id.locationId,
              location: asset._id.location,
              ...commonFilterIds,
            },
            ...commonFileterUserReport,
          };

          if (userReport.length > 0) {
            filterUserReport = userReport[0];
          }
          userReportRows.push(filterUserReport);
        }
      }
    }
  }

  return userReportRows;
};

exports.calculateReportDataAndAddKeys = (scopeReports, userReportRows) => {
  let totalCompletionPercentage = 0;
  let sumAllReportProgress = 0;
  for (let scope of scopeReports.scopeData) {
    for (let report of scope.reports) {
      let calReportProgressAndComplition = 0;
      for (let userReport of userReportRows) {
        let reportComplitionTotal = 0;
        if (report._id.toString() === userReport._id.reportId.toString()) {
          reportComplitionTotal += userReport.reportComplition;
          report.reportComplitionProgress = reportComplitionTotal;
          if (report.totalDurationReport > 0) {
            let reportComplition =
              ((reportComplitionTotal * report.durationReport) / report.totalDurationReport) * 100;

            report.totalReportComplition = Number(reportComplition.toFixed(2));
            calReportProgressAndComplition = reportComplition * report.reportProgress;
          }
        }
      }
      sumAllReportProgress += calReportProgressAndComplition;
    }
  }

  totalCompletionPercentage = sumAllReportProgress / 100;
  scopeReports.totalCompletionPercentage = Number(totalCompletionPercentage.toFixed(2));

  return scopeReports;
};

/**
 * Get Detailed Progress Report Data
 *
 * @param {Object} filter
 * @param {Date} dprDate
 * @returns {Object} Detailed Progress Report Data
 */
exports.getDetailedProgressReportData = async (filter, dprDate, sortOrder) => {
  const { startDprDate, endDprDate } = commonfunctionsUtils.getStartAndEndDates(dprDate);

  const pipeline = [
    {
      $match: {
        account: filter.account,
        deletedAt: null,
        createdAt: {
          $gte: startDprDate,
          $lt: endDprDate,
        },
        answers: { $ne: [] },
      },
    },
    {
      $project: {
        _id: 1,
        reportQuestion: 1,
        userReport: 1,
        report: 1,
        createdAt: 1,
        answers: 1,
      },
    },
    {
      $lookup: {
        from: 'user-reports',
        localField: 'userReport',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              project: filter.project,
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              project: 1,
              report: 1,
              location: 1,
              asset: 1,
            },
          },
        ],
        as: 'userReport',
      },
    },
    {
      $unwind: '$userReport',
    },
    // Unwind the asset array to handle multiple assets
    {
      $unwind: '$userReport.asset',
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: 'reportQuestion',
        foreignField: '_id',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              isRequired: 1,
              report: 1,
            },
          },
        ],
        as: 'reportQuestion',
      },
    },
    {
      $unwind: '$reportQuestion',
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'reportQuestion.report',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              deletedAt: null,
              isProgressable: true,
              isPublish: true,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              project: 1,
              type: 1,
            },
          },
        ],
        as: 'report',
      },
    },
    {
      $unwind: '$report',
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'userReport.location',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
            },
          },
        ],
        as: 'location',
      },
    },
    {
      $unwind: '$location',
    },
    {
      $lookup: {
        from: 'assets',
        localField: 'userReport.asset.asset',
        foreignField: '_id',
        pipeline: [
          {
            $lookup: {
              from: 'locations',
              localField: 'fromLocation',
              foreignField: '_id',
              as: 'fromLocation',
            },
          },
          {
            $unwind: '$fromLocation',
          },
          {
            $lookup: {
              from: 'locations',
              localField: 'toLocation',
              foreignField: '_id',
              as: 'toLocation',
            },
          },
          { $unwind: '$toLocation' },
          {
            $project: {
              _id: 1,
              fromLocation: { _id: 1, title: 1 },
              toLocation: { _id: 1, title: 1 },
              cableName: 1,
              reports: 1,
            },
          },
        ],
        as: 'asset',
      },
    },
    {
      $unwind: '$asset',
    },
    {
      $lookup: {
        from: 'report-question-answers',
        localField: 'reportQuestion._id',
        foreignField: 'reportQuestion',
        pipeline: [
          {
            $match: {
              deletedAt: null,
              title: {
                $elemMatch: {
                  isRequired: true,
                },
              },
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              option: 1,
            },
          },
        ],
        as: 'reportQuestionAnswers',
      },
    },
    // Lookup asset reports and their details
    {
      $lookup: {
        from: 'reports',
        localField: 'asset.reports',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              deletedAt: null,
              isProgressable: true,
              isPublish: true,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              type: 1,
            },
          },
        ],
        as: 'assetReports',
      },
    },
    // Lookup asset report questions
    {
      $lookup: {
        from: 'report-questions',
        localField: 'assetReports._id',
        foreignField: 'report',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              isRequired: 1,
              report: 1,
            },
          },
        ],
        as: 'assetReportQuestions',
      },
    },
    // Lookup asset report question answers
    {
      $lookup: {
        from: 'report-question-answers',
        localField: 'assetReportQuestions._id',
        foreignField: 'reportQuestion',
        pipeline: [
          {
            $match: {
              deletedAt: null,
              title: {
                $elemMatch: {
                  isRequired: true,
                },
              },
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              option: 1,
            },
          },
        ],
        as: 'assetReportQuestionAnswers',
      },
    },
    // Lookup asset user report answers
    {
      $lookup: {
        from: 'user-report-answers',
        localField: 'assetReportQuestionAnswers.title._id',
        foreignField: 'answers.answerTitleId',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              answers: 1,
            },
          },
        ],
        as: 'assetUserReportAnswers',
      },
    },
    {
      $group: {
        _id: {
          locationId: '$location._id',
          reportId: '$report._id',
          reportTitle: '$report.title',
          assetId: '$asset._id',
        },
        locationTitle: { $first: '$location.title' },
        project: { $first: '$userReport.project' },
        createdAt: { $first: '$createdAt' },
        reportType: { $first: '$report.type' },
        reportQuestions: {
          $addToSet: {
            _id: '$reportQuestion._id',
            title: '$reportQuestion.title',
            isRequired: '$reportQuestion.isRequired',
          },
        },
        reportQuestionAnswers: { $addToSet: '$reportQuestionAnswers' },
        userReportAnswers: {
          $addToSet: {
            _id: '$_id',
            answers: '$answers',
          },
        },
        assetData: {
          $first: {
            _id: '$asset._id',
            cableName: '$asset.cableName',
            assetLocations: {
              fromLocation: '$asset.fromLocation.title',
              toLocation: '$asset.toLocation.title',
            },
            assetsReports: '$assetReports',
            reportQuestions: '$assetReportQuestions',
            reportQuestionAnswers: '$assetReportQuestionAnswers',
            userReportAnswers: '$assetUserReportAnswers',
          },
        },
      },
    },
    // Group by location and report to collect assets for each report
    {
      $group: {
        _id: {
          locationId: '$_id.locationId',
          reportId: '$_id.reportId',
          reportTitle: '$_id.reportTitle',
        },
        locationTitle: { $first: '$locationTitle' },
        project: { $first: '$project' },
        createdAt: { $first: '$createdAt' },
        reportType: { $first: '$reportType' },
        reportQuestions: { $first: '$reportQuestions' },
        reportQuestionAnswers: { $first: '$reportQuestionAnswers' },
        userReportAnswers: { $first: '$userReportAnswers' },
        assets: {
          $addToSet: '$assetData',
        },
      },
    },
    // Final grouping by location to match the expected response structure
    {
      $group: {
        _id: '$_id.locationId',
        title: { $first: '$locationTitle' },
        project: { $first: '$project' },
        createdAt: { $first: '$createdAt' },
        assets: {
          $reduce: {
            input: '$assets',
            initialValue: [],
            in: { $concatArrays: ['$$value', '$$this'] }
          }
        },
        locationReports: {
          $addToSet: {
            _id: '$_id.reportId',
            title: '$_id.reportTitle',
            type: '$reportType',
            reportQuestions: '$reportQuestions',
            reportQuestionAnswers: '$reportQuestionAnswers',
            userReportAnswers: '$userReportAnswers',
          },
        },
      },
    },
    {
      $sort: {
        createdAt: sortOrder,
      },
    },
  ];

  return await UserReportAnswer.aggregate(pipeline);
};
